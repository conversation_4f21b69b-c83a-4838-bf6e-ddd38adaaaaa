#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书爬虫使用示例
"""

import os
import json
from xiaohongshu_selenium_crawler import XiaohongshuSeleniumCrawler


def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 创建爬虫实例
    crawler = XiaohongshuSeleniumCrawler(
        output_dir="example_data",
        headless=False  # 显示浏览器窗口，便于观察
    )
    
    # 爬取美食相关推文
    crawler.search_and_crawl(
        keyword="美食",
        max_posts=5,  # 少量测试
        download_images=True
    )


def example_batch_crawl():
    """批量爬取示例"""
    print("=== 批量爬取示例 ===")
    
    # 定义要爬取的关键词列表
    keywords = ["美食", "旅行", "穿搭", "护肤", "健身"]
    
    crawler = XiaohongshuSeleniumCrawler(
        output_dir="batch_data",
        headless=True  # 无头模式，提高效率
    )
    
    for keyword in keywords:
        print(f"\n正在爬取关键词: {keyword}")
        try:
            crawler.search_and_crawl(
                keyword=keyword,
                max_posts=10,
                download_images=True
            )
        except Exception as e:
            print(f"爬取 {keyword} 时出错: {str(e)}")
            continue


def example_analyze_data():
    """数据分析示例"""
    print("=== 数据分析示例 ===")
    
    data_dir = "xiaohongshu_data/posts"
    
    if not os.path.exists(data_dir):
        print("没有找到数据目录，请先运行爬虫")
        return
    
    # 统计信息
    total_posts = 0
    total_images = 0
    authors = set()
    keywords = {}
    
    # 遍历所有JSON文件
    for filename in os.listdir(data_dir):
        if filename.endswith('.json'):
            filepath = os.path.join(data_dir, filename)
            
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    post_data = json.load(f)
                
                total_posts += 1
                total_images += len(post_data.get('images', []))
                authors.add(post_data.get('author', ''))
                
                # 从文件名提取关键词
                keyword = filename.split('_')[0]
                keywords[keyword] = keywords.get(keyword, 0) + 1
                
            except Exception as e:
                print(f"读取文件 {filename} 失败: {str(e)}")
    
    # 输出统计结果
    print(f"\n数据统计结果:")
    print(f"总推文数: {total_posts}")
    print(f"总图片数: {total_images}")
    print(f"作者数量: {len(authors)}")
    print(f"平均每篇推文图片数: {total_images/total_posts if total_posts > 0 else 0:.2f}")
    
    print(f"\n关键词分布:")
    for keyword, count in sorted(keywords.items(), key=lambda x: x[1], reverse=True):
        print(f"  {keyword}: {count} 篇")


def example_custom_config():
    """自定义配置示例"""
    print("=== 自定义配置示例 ===")
    
    # 创建自定义配置的爬虫
    crawler = XiaohongshuSeleniumCrawler(
        output_dir="custom_data",
        headless=True
    )
    
    # 可以通过修改crawler的属性来自定义行为
    # 例如：调整等待时间、滚动次数等
    
    crawler.search_and_crawl(
        keyword="数码",
        max_posts=15,
        download_images=False  # 不下载图片，只获取文本信息
    )


def main():
    """主函数"""
    print("小红书爬虫使用示例")
    print("=" * 50)
    
    while True:
        print("\n请选择示例:")
        print("1. 基础使用示例")
        print("2. 批量爬取示例")
        print("3. 数据分析示例")
        print("4. 自定义配置示例")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '1':
            example_basic_usage()
        elif choice == '2':
            example_batch_crawl()
        elif choice == '3':
            example_analyze_data()
        elif choice == '4':
            example_custom_config()
        elif choice == '0':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
