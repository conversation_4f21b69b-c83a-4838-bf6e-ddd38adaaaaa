#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动Web界面的主入口文件

注意：请确保在虚拟环境中运行此脚本
Windows: venv\Scripts\activate.bat
Linux/macOS: source venv/bin/activate
"""

import sys
import os
from pathlib import Path

# 检查是否在虚拟环境中
if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
    print("⚠️  警告：未检测到虚拟环境")
    print("请先激活虚拟环境：")
    print("Windows: venv\\Scripts\\activate.bat")
    print("Linux/macOS: source venv/bin/activate")
    print("或使用启动脚本：scripts/run_web.bat (Windows) 或 scripts/run_web.sh (Linux/macOS)")
    sys.exit(1)

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入Web应用
from src.web.app import app, socketio

if __name__ == '__main__':
    print("🚀 小红书爬虫Web界面启动中...")
    print("📱 访问地址: http://localhost:5000")
    print("⚠️  请确保已安装所有依赖包")
    
    # 确保输出目录存在
    output_dir = project_root / 'data' / 'output'
    output_dir.mkdir(parents=True, exist_ok=True)
    (output_dir / 'posts').mkdir(exist_ok=True)
    (output_dir / 'images').mkdir(exist_ok=True)
    (output_dir / 'cache').mkdir(exist_ok=True)
    
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)
