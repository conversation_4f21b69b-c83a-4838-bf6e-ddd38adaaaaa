#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书高级爬虫 - 基于Playwright
集成最新反爬虫技术和MediaCrawler架构
"""

import asyncio
import json
import os
import time
import hashlib
import re
import random
from datetime import datetime
from typing import List, Dict, Optional, Any
from pathlib import Path
import logging
from urllib.parse import urljoin, urlparse

from playwright.async_api import async_playwright, Page, Browser, BrowserContext
import aiohttp
from tenacity import retry, stop_after_attempt, wait_exponential
from loguru import logger
import pandas as pd
from pydantic import BaseModel, Field

from .anti_detection import AntiDetectionManager


class PostInfo(BaseModel):
    """推文信息数据模型"""
    id: str = Field(..., description="推文ID")
    title: str = Field(default="", description="推文标题")
    content: str = Field(default="", description="推文内容")
    author: str = Field(default="", description="作者昵称")
    author_id: str = Field(default="", description="作者ID")
    like_count: int = Field(default=0, description="点赞数")
    comment_count: int = Field(default=0, description="评论数")
    share_count: int = Field(default=0, description="分享数")
    create_time: str = Field(default="", description="发布时间")
    images: List[str] = Field(default_factory=list, description="图片URL列表")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    url: str = Field(default="", description="推文链接")
    note_type: str = Field(default="", description="笔记类型")


class XiaohongshuAdvancedCrawler:
    """小红书高级爬虫类"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.output_dir = Path(self.config.get('output_dir', 'xiaohongshu_data'))
        self.headless = self.config.get('headless', False)
        self.request_delay = self.config.get('request_delay', 2)
        self.max_posts = self.config.get('max_posts', 50)

        # 初始化目录
        self.setup_directories()

        # Playwright相关
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None

        # 反检测管理器
        self.anti_detection = AntiDetectionManager()

        # 会话管理
        self.session = None
        self.cookies = {}
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://www.xiaohongshu.com/',
            'Origin': 'https://www.xiaohongshu.com',
        }
        
    def setup_directories(self):
        """创建必要的目录"""
        self.output_dir.mkdir(exist_ok=True)
        (self.output_dir / 'posts').mkdir(exist_ok=True)
        (self.output_dir / 'images').mkdir(exist_ok=True)
        (self.output_dir / 'cache').mkdir(exist_ok=True)
        
    async def init_browser(self):
        """初始化浏览器"""
        self.playwright = await async_playwright().start()

        # 浏览器配置
        browser_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-blink-features=AutomationControlled',
            '--disable-dev-shm-usage',
            '--disable-extensions',
            '--no-first-run',
            '--disable-default-apps',
        ]

        # 获取代理配置
        proxy_config = self.anti_detection.get_proxy_config()

        self.browser = await self.playwright.chromium.launch(
            headless=self.headless,
            args=browser_args,
            proxy=proxy_config
        )

        # 创建上下文
        context_options = {
            'viewport': {'width': 1920, 'height': 1080},
            'user_agent': self.anti_detection.ua_rotator.get_random_user_agent(),
            'locale': 'zh-CN',
            'timezone_id': 'Asia/Shanghai',
            'permissions': ['geolocation'],
            'geolocation': {'latitude': 39.9042, 'longitude': 116.4074},  # 北京坐标
        }

        if proxy_config:
            context_options['proxy'] = proxy_config

        self.context = await self.browser.new_context(**context_options)

        self.page = await self.context.new_page()

        # 设置反检测
        await self.anti_detection.setup_page_stealth(self.page)

        # 设置增强的请求头
        enhanced_headers = await self.anti_detection.get_enhanced_headers(
            self.page, 'https://www.xiaohongshu.com'
        )
        await self.page.set_extra_http_headers(enhanced_headers)

        logger.info("✓ 浏览器初始化成功")
        
    async def close_browser(self):
        """关闭浏览器"""
        if self.page:
            await self.page.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()
        logger.info("✓ 浏览器已关闭")
        
    async def login_by_qrcode(self):
        """二维码登录"""
        logger.info("开始二维码登录流程...")
        
        try:
            # 访问登录页面
            await self.page.goto('https://www.xiaohongshu.com')
            await asyncio.sleep(3)
            
            # 点击登录按钮
            try:
                login_button = await self.page.wait_for_selector('.login-btn, .sign-in', timeout=10000)
                await login_button.click()
                await asyncio.sleep(2)
            except:
                logger.info("未找到登录按钮，可能已经登录")
                
            # 等待二维码出现
            try:
                qr_code = await self.page.wait_for_selector('.qrcode img, .qr-img', timeout=15000)
                logger.info("✓ 二维码已显示，请使用小红书APP扫码登录")
                
                # 等待登录成功
                await self.page.wait_for_url('**/explore**', timeout=120000)
                logger.info("✓ 登录成功！")
                
                # 保存登录状态
                await self.save_login_state()
                
            except Exception as e:
                logger.error(f"二维码登录失败: {str(e)}")
                return False
                
        except Exception as e:
            logger.error(f"登录过程出错: {str(e)}")
            return False
            
        return True
        
    async def save_login_state(self):
        """保存登录状态"""
        try:
            # 保存cookies
            cookies = await self.context.cookies()
            cookies_file = self.output_dir / 'cache' / 'cookies.json'
            with open(cookies_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)
                
            # 保存localStorage
            local_storage = await self.page.evaluate('() => JSON.stringify(localStorage)')
            storage_file = self.output_dir / 'cache' / 'localStorage.json'
            with open(storage_file, 'w', encoding='utf-8') as f:
                f.write(local_storage)
                
            logger.info("✓ 登录状态已保存")
            
        except Exception as e:
            logger.error(f"保存登录状态失败: {str(e)}")
            
    async def load_login_state(self):
        """加载登录状态"""
        try:
            # 加载cookies
            cookies_file = self.output_dir / 'cache' / 'cookies.json'
            if cookies_file.exists():
                with open(cookies_file, 'r', encoding='utf-8') as f:
                    cookies = json.load(f)
                await self.context.add_cookies(cookies)
                
            # 加载localStorage
            storage_file = self.output_dir / 'cache' / 'localStorage.json'
            if storage_file.exists():
                with open(storage_file, 'r', encoding='utf-8') as f:
                    local_storage = f.read()
                    
                await self.page.goto('https://www.xiaohongshu.com')
                await self.page.evaluate(f'localStorage.clear(); Object.assign(localStorage, {local_storage})')
                
            logger.info("✓ 登录状态已加载")
            return True
            
        except Exception as e:
            logger.error(f"加载登录状态失败: {str(e)}")
            return False
            
    async def get_x_s_common(self, url: str, data: Dict = None) -> str:
        """获取x-s-common参数"""
        try:
            # 这里需要根据最新的小红书加密算法来实现
            # 可以通过执行页面中的JS来获取
            x_s_common = await self.page.evaluate("""
                (url, data) => {
                    // 这里需要实现具体的x-s-common生成逻辑
                    // 根据最新的小红书算法
                    return window.generateXSCommon ? window.generateXSCommon(url, data) : '';
                }
            """, url, data)
            
            return x_s_common or ""
            
        except Exception as e:
            logger.error(f"获取x-s-common失败: {str(e)}")
            return ""
            
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def search_posts(self, keyword: str, page: int = 1) -> List[Dict]:
        """搜索推文"""
        try:
            search_url = f"https://www.xiaohongshu.com/search_result?keyword={keyword}&page={page}"

            # 等待请求间隔
            await self.anti_detection.wait_with_jitter()

            await self.page.goto(search_url)
            await asyncio.sleep(3)

            # 检查是否需要处理验证
            if not await self.anti_detection.handle_verification(self.page):
                logger.warning("遇到验证码，跳过当前请求")
                return []

            # 模拟人类行为
            await self.anti_detection.simulate_human_behavior(self.page)

            # 滚动加载更多内容
            for i in range(5):
                await self.page.evaluate('window.scrollTo(0, document.body.scrollHeight)')
                await asyncio.sleep(random.uniform(1.5, 3.0))
                
            # 提取推文数据
            posts_data = await self.page.evaluate("""
                () => {
                    const posts = [];
                    const noteItems = document.querySelectorAll('.note-item, .feeds-container .note');
                    
                    noteItems.forEach(item => {
                        try {
                            const linkEl = item.querySelector('a');
                            const titleEl = item.querySelector('.title, .note-title');
                            const authorEl = item.querySelector('.author, .user-name');
                            const imgEls = item.querySelectorAll('img');
                            
                            const post = {
                                id: '',
                                title: titleEl ? titleEl.textContent.trim() : '',
                                author: authorEl ? authorEl.textContent.trim() : '',
                                url: linkEl ? linkEl.href : '',
                                images: []
                            };
                            
                            // 提取ID
                            if (post.url) {
                                const match = post.url.match(/\/explore\/([a-zA-Z0-9]+)/);
                                if (match) post.id = match[1];
                            }
                            
                            // 提取图片
                            imgEls.forEach(img => {
                                if (img.src && !img.src.includes('avatar')) {
                                    post.images.push(img.src);
                                }
                            });
                            
                            if (post.id && post.title) {
                                posts.push(post);
                            }
                        } catch (e) {
                            console.error('提取推文数据失败:', e);
                        }
                    });
                    
                    return posts;
                }
            """)
            
            logger.info(f"搜索到 {len(posts_data)} 条推文")
            return posts_data
            
        except Exception as e:
            logger.error(f"搜索推文失败: {str(e)}")
            return []
            
    async def get_post_detail(self, post_id: str) -> Optional[PostInfo]:
        """获取推文详情"""
        try:
            detail_url = f"https://www.xiaohongshu.com/explore/{post_id}"
            
            await self.page.goto(detail_url)
            await asyncio.sleep(3)
            
            # 提取详细信息
            post_detail = await self.page.evaluate("""
                () => {
                    try {
                        const detail = {
                            title: '',
                            content: '',
                            author: '',
                            like_count: 0,
                            comment_count: 0,
                            images: [],
                            tags: []
                        };
                        
                        // 提取标题和内容
                        const titleEl = document.querySelector('.note-title, .title');
                        if (titleEl) detail.title = titleEl.textContent.trim();
                        
                        const contentEl = document.querySelector('.note-content, .content');
                        if (contentEl) detail.content = contentEl.textContent.trim();
                        
                        // 提取作者
                        const authorEl = document.querySelector('.user-name, .author');
                        if (authorEl) detail.author = authorEl.textContent.trim();
                        
                        // 提取互动数据
                        const likeEl = document.querySelector('.like-count, .interactions .like');
                        if (likeEl) {
                            const likeText = likeEl.textContent.trim();
                            detail.like_count = parseInt(likeText.replace(/[^0-9]/g, '')) || 0;
                        }
                        
                        // 提取图片
                        const imgEls = document.querySelectorAll('.note-image img, .carousel img');
                        imgEls.forEach(img => {
                            if (img.src && !img.src.includes('avatar')) {
                                detail.images.push(img.src);
                            }
                        });
                        
                        // 提取标签
                        const tagEls = document.querySelectorAll('.tag, .topic');
                        tagEls.forEach(tag => {
                            detail.tags.push(tag.textContent.trim());
                        });
                        
                        return detail;
                    } catch (e) {
                        console.error('提取推文详情失败:', e);
                        return {};
                    }
                }
            """)
            
            if post_detail:
                post_info = PostInfo(
                    id=post_id,
                    url=f"https://www.xiaohongshu.com/explore/{post_id}",
                    **post_detail
                )
                return post_info
                
        except Exception as e:
            logger.error(f"获取推文详情失败 {post_id}: {str(e)}")
            
        return None
        
    async def download_image(self, image_url: str, save_path: Path) -> bool:
        """下载图片"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(
                    headers=self.headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                )
                
            async with self.session.get(image_url) as response:
                if response.status == 200:
                    content = await response.read()
                    with open(save_path, 'wb') as f:
                        f.write(content)
                    logger.info(f"✓ 图片下载成功: {save_path.name}")
                    return True
                    
        except Exception as e:
            logger.error(f"图片下载失败 {image_url}: {str(e)}")
            
        return False
        
    def clean_filename(self, filename: str) -> str:
        """清理文件名"""
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        return filename[:100] if len(filename) > 100 else filename
        
    async def save_post_data(self, post_info: PostInfo, keyword: str):
        """保存推文数据"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{keyword}_{post_info.id}_{timestamp}.json"
            filename = self.clean_filename(filename)
            
            filepath = self.output_dir / 'posts' / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(post_info.model_dump(), f, ensure_ascii=False, indent=2)
                
            logger.info(f"✓ 推文数据已保存: {filename}")
            
        except Exception as e:
            logger.error(f"保存推文数据失败: {str(e)}")
            
    async def download_post_images(self, post_info: PostInfo, keyword: str) -> List[str]:
        """下载推文图片"""
        downloaded_images = []
        
        for i, image_url in enumerate(post_info.images):
            try:
                # 处理URL
                if image_url.startswith('//'):
                    image_url = 'https:' + image_url
                elif image_url.startswith('/'):
                    image_url = 'https://www.xiaohongshu.com' + image_url
                    
                # 生成文件名
                url_hash = hashlib.md5(image_url.encode()).hexdigest()[:8]
                ext = '.jpg'
                
                parsed_url = urlparse(image_url)
                if parsed_url.path:
                    path_ext = os.path.splitext(parsed_url.path)[1]
                    if path_ext in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
                        ext = path_ext
                        
                filename = f"{keyword}_{post_info.id}_{i+1}_{url_hash}{ext}"
                filename = self.clean_filename(filename)
                
                save_path = self.output_dir / 'images' / filename
                
                if await self.download_image(image_url, save_path):
                    downloaded_images.append(str(save_path))
                    
                await asyncio.sleep(1)  # 下载间隔
                
            except Exception as e:
                logger.error(f"处理图片失败 {image_url}: {str(e)}")
                
        return downloaded_images

    async def crawl_by_keyword(self, keyword: str, max_posts: int = None, download_images: bool = True):
        """根据关键词爬取推文"""
        max_posts = max_posts or self.max_posts
        logger.info(f"开始爬取关键词: {keyword}, 最大数量: {max_posts}")

        try:
            # 初始化浏览器
            await self.init_browser()

            # 尝试加载登录状态
            login_loaded = await self.load_login_state()

            if not login_loaded:
                # 需要重新登录
                success = await self.login_by_qrcode()
                if not success:
                    logger.error("登录失败，无法继续爬取")
                    return

            crawled_count = 0
            page = 1

            while crawled_count < max_posts:
                logger.info(f"正在爬取第 {page} 页...")

                # 搜索推文
                posts = await self.search_posts(keyword, page)

                if not posts:
                    logger.info("没有更多推文了")
                    break

                for post_data in posts:
                    if crawled_count >= max_posts:
                        break

                    try:
                        # 获取详细信息
                        post_info = await self.get_post_detail(post_data['id'])

                        if post_info:
                            logger.info(f"处理推文 {crawled_count + 1}: {post_info.title[:50]}...")

                            # 保存推文数据
                            await self.save_post_data(post_info, keyword)

                            # 下载图片
                            if download_images and post_info.images:
                                downloaded_images = await self.download_post_images(post_info, keyword)
                                logger.info(f"下载了 {len(downloaded_images)} 张图片")

                            crawled_count += 1

                        # 请求间隔
                        await asyncio.sleep(self.request_delay)

                    except Exception as e:
                        logger.error(f"处理推文失败: {str(e)}")
                        continue

                page += 1

            logger.info(f"爬取完成，共处理 {crawled_count} 条推文")

        except Exception as e:
            logger.error(f"爬取过程出错: {str(e)}")
        finally:
            # 关闭会话
            if self.session:
                await self.session.close()
            # 关闭浏览器
            await self.close_browser()

    async def export_to_csv(self, keyword: str):
        """导出数据到CSV"""
        try:
            posts_dir = self.output_dir / 'posts'
            csv_file = self.output_dir / f"{keyword}_posts.csv"

            posts_data = []

            for json_file in posts_dir.glob(f"{keyword}_*.json"):
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        post_data = json.load(f)
                        posts_data.append(post_data)
                except Exception as e:
                    logger.error(f"读取文件失败 {json_file}: {str(e)}")

            if posts_data:
                df = pd.DataFrame(posts_data)
                df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                logger.info(f"✓ 数据已导出到CSV: {csv_file}")
            else:
                logger.warning("没有找到数据文件")

        except Exception as e:
            logger.error(f"导出CSV失败: {str(e)}")

    def get_statistics(self, keyword: str) -> Dict[str, Any]:
        """获取爬取统计信息"""
        try:
            posts_dir = self.output_dir / 'posts'
            images_dir = self.output_dir / 'images'

            # 统计推文数量
            post_files = list(posts_dir.glob(f"{keyword}_*.json"))
            post_count = len(post_files)

            # 统计图片数量
            image_files = list(images_dir.glob(f"{keyword}_*"))
            image_count = len(image_files)

            # 统计作者数量
            authors = set()
            total_likes = 0
            total_comments = 0

            for json_file in post_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        post_data = json.load(f)
                        authors.add(post_data.get('author', ''))
                        total_likes += post_data.get('like_count', 0)
                        total_comments += post_data.get('comment_count', 0)
                except:
                    continue

            stats = {
                'keyword': keyword,
                'post_count': post_count,
                'image_count': image_count,
                'author_count': len(authors),
                'total_likes': total_likes,
                'total_comments': total_comments,
                'avg_likes_per_post': total_likes / post_count if post_count > 0 else 0,
                'avg_images_per_post': image_count / post_count if post_count > 0 else 0
            }

            return stats

        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}


async def main():
    """主函数示例"""
    # 配置
    config = {
        'output_dir': 'xiaohongshu_data',
        'headless': False,
        'request_delay': 2,
        'max_posts': 20
    }

    # 创建爬虫实例
    crawler = XiaohongshuAdvancedCrawler(config)

    # 获取用户输入
    keyword = input("请输入搜索关键词: ").strip()
    if not keyword:
        print("关键词不能为空")
        return

    try:
        max_posts = int(input("请输入最大爬取数量 (默认20): ") or "20")
    except ValueError:
        max_posts = 20

    download_images = input("是否下载图片? (y/n, 默认y): ").strip().lower() != 'n'

    # 开始爬取
    await crawler.crawl_by_keyword(keyword, max_posts, download_images)

    # 导出CSV
    await crawler.export_to_csv(keyword)

    # 显示统计信息
    stats = crawler.get_statistics(keyword)
    if stats:
        print("\n" + "="*50)
        print("📊 爬取统计信息")
        print("="*50)
        print(f"关键词: {stats['keyword']}")
        print(f"推文数量: {stats['post_count']}")
        print(f"图片数量: {stats['image_count']}")
        print(f"作者数量: {stats['author_count']}")
        print(f"总点赞数: {stats['total_likes']}")
        print(f"平均每篇点赞: {stats['avg_likes_per_post']:.1f}")
        print(f"平均每篇图片: {stats['avg_images_per_post']:.1f}")
        print("="*50)


if __name__ == "__main__":
    asyncio.run(main())
