#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

# 爬虫配置
CRAWLER_CONFIG = {
    # 输出目录
    'output_dir': 'xiaohongshu_data',
    
    # 请求配置
    'request_timeout': 30,
    'request_delay': 2,  # 请求间隔（秒）
    
    # 图片下载配置
    'download_images': True,
    'image_timeout': 30,
    'image_delay': 1,  # 图片下载间隔（秒）
    
    # Selenium配置
    'headless': False,  # 是否使用无头模式
    'window_size': '1920,1080',
    'page_load_timeout': 10,
    'scroll_count': 5,  # 滚动次数
    'scroll_delay': 2,  # 滚动间隔（秒）
    
    # 文件配置
    'max_filename_length': 100,
    'supported_image_formats': ['.jpg', '.jpeg', '.png', '.webp', '.gif'],
    
    # 日志配置
    'log_level': 'INFO',
    'log_file': 'xiaohongshu_crawler.log',
}

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Referer': 'https://www.xiaohongshu.com/'
}

# CSS选择器配置
SELECTORS = {
    'post_containers': [
        ".note-item",
        ".feeds-container .note",
        ".search-result .note",
        "[data-v-*] .note",
        ".waterfall-item"
    ],
    'title': [
        "a[title]",
        ".title",
        ".note-title",
        ".content"
    ],
    'author': [
        ".author",
        ".user-name",
        ".nickname",
        ".username"
    ],
    'images': [
        "img[src*='http']",
        ".image img",
        ".cover img"
    ],
    'link': [
        "a[href*='/explore/']",
        "a[href*='/discovery/']"
    ]
}

# URL配置
URLS = {
    'base_url': 'https://www.xiaohongshu.com',
    'search_url': 'https://www.xiaohongshu.com/search_result?keyword={}',
    'api_base': 'https://www.xiaohongshu.com/api'
}
