#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书爬虫Web界面
基于Flask的用户友好界面
"""

import os
import json
import asyncio
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
from flask_socketio import SocketIO, emit
import pandas as pd

from xiaohongshu_advanced_crawler import XiaohongshuAdvancedCrawler
from config import CRAWLER_CONFIG


app = Flask(__name__)
app.config['SECRET_KEY'] = 'xiaohongshu_crawler_secret_key'
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局变量
crawler_instance = None
crawl_status = {
    'running': False,
    'progress': 0,
    'total': 0,
    'current_post': '',
    'message': ''
}


class WebSocketLogger:
    """WebSocket日志处理器"""
    
    def __init__(self, socketio):
        self.socketio = socketio
        
    def info(self, message):
        self.socketio.emit('log', {'level': 'info', 'message': message})
        
    def error(self, message):
        self.socketio.emit('log', {'level': 'error', 'message': message})
        
    def warning(self, message):
        self.socketio.emit('log', {'level': 'warning', 'message': message})


@app.route('/')
def index():
    """主页"""
    return render_template('index.html')


@app.route('/api/config', methods=['GET'])
def get_config():
    """获取配置信息"""
    return jsonify({
        'output_dir': CRAWLER_CONFIG.get('output_dir', 'xiaohongshu_data'),
        'max_posts': CRAWLER_CONFIG.get('max_posts', 50),
        'request_delay': CRAWLER_CONFIG.get('request_delay', 2),
        'headless': CRAWLER_CONFIG.get('headless', False),
        'download_images': CRAWLER_CONFIG.get('download_images', True)
    })


@app.route('/api/config', methods=['POST'])
def update_config():
    """更新配置"""
    try:
        data = request.get_json()
        
        # 更新配置
        CRAWLER_CONFIG.update(data)
        
        return jsonify({'success': True, 'message': '配置更新成功'})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'配置更新失败: {str(e)}'})


@app.route('/api/start_crawl', methods=['POST'])
def start_crawl():
    """开始爬取"""
    global crawler_instance, crawl_status
    
    try:
        if crawl_status['running']:
            return jsonify({'success': False, 'message': '爬虫正在运行中'})
            
        data = request.get_json()
        keyword = data.get('keyword', '').strip()
        max_posts = data.get('max_posts', 50)
        download_images = data.get('download_images', True)
        output_dir = data.get('output_dir', 'xiaohongshu_data')
        
        if not keyword:
            return jsonify({'success': False, 'message': '关键词不能为空'})
            
        # 更新状态
        crawl_status.update({
            'running': True,
            'progress': 0,
            'total': max_posts,
            'current_post': '',
            'message': '正在初始化...'
        })
        
        # 创建爬虫配置
        config = CRAWLER_CONFIG.copy()
        config.update({
            'output_dir': output_dir,
            'max_posts': max_posts,
            'download_images': download_images
        })
        
        # 在新线程中运行爬虫
        thread = threading.Thread(
            target=run_crawler_async,
            args=(keyword, config)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({'success': True, 'message': '爬取任务已启动'})
        
    except Exception as e:
        crawl_status['running'] = False
        return jsonify({'success': False, 'message': f'启动失败: {str(e)}'})


def run_crawler_async(keyword: str, config: Dict[str, Any]):
    """异步运行爬虫"""
    global crawler_instance, crawl_status
    
    try:
        # 创建新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 运行爬虫
        loop.run_until_complete(run_crawler(keyword, config))
        
    except Exception as e:
        socketio.emit('log', {'level': 'error', 'message': f'爬取失败: {str(e)}'})
    finally:
        crawl_status['running'] = False
        socketio.emit('crawl_finished', {'success': True})


async def run_crawler(keyword: str, config: Dict[str, Any]):
    """运行爬虫"""
    global crawler_instance, crawl_status
    
    try:
        # 创建爬虫实例
        crawler_instance = XiaohongshuAdvancedCrawler(config)
        
        # 发送开始消息
        socketio.emit('log', {'level': 'info', 'message': f'开始爬取关键词: {keyword}'})
        
        # 开始爬取
        await crawler_instance.crawl_by_keyword(
            keyword=keyword,
            max_posts=config.get('max_posts', 50),
            download_images=config.get('download_images', True)
        )
        
        # 导出CSV
        await crawler_instance.export_to_csv(keyword)
        
        # 获取统计信息
        stats = crawler_instance.get_statistics(keyword)
        
        # 发送完成消息
        socketio.emit('crawl_complete', {
            'keyword': keyword,
            'stats': stats
        })
        
        socketio.emit('log', {'level': 'info', 'message': '爬取任务完成！'})
        
    except Exception as e:
        socketio.emit('log', {'level': 'error', 'message': f'爬取过程出错: {str(e)}'})
        raise


@app.route('/api/stop_crawl', methods=['POST'])
def stop_crawl():
    """停止爬取"""
    global crawl_status
    
    try:
        crawl_status['running'] = False
        socketio.emit('log', {'level': 'warning', 'message': '爬取任务已停止'})
        
        return jsonify({'success': True, 'message': '爬取已停止'})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'停止失败: {str(e)}'})


@app.route('/api/status', methods=['GET'])
def get_status():
    """获取爬取状态"""
    return jsonify(crawl_status)


@app.route('/api/results/<keyword>', methods=['GET'])
def get_results(keyword):
    """获取爬取结果"""
    try:
        output_dir = Path(CRAWLER_CONFIG.get('output_dir', 'xiaohongshu_data'))
        posts_dir = output_dir / 'posts'
        
        results = []
        
        for json_file in posts_dir.glob(f"{keyword}_*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    post_data = json.load(f)
                    results.append(post_data)
            except Exception as e:
                continue
                
        return jsonify({
            'success': True,
            'data': results,
            'count': len(results)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取结果失败: {str(e)}'})


@app.route('/api/download/<keyword>', methods=['GET'])
def download_results(keyword):
    """下载结果文件"""
    try:
        output_dir = Path(CRAWLER_CONFIG.get('output_dir', 'xiaohongshu_data'))
        csv_file = output_dir / f"{keyword}_posts.csv"
        
        if csv_file.exists():
            return send_file(
                csv_file,
                as_attachment=True,
                download_name=f"{keyword}_posts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            )
        else:
            return jsonify({'success': False, 'message': 'CSV文件不存在'})
            
    except Exception as e:
        return jsonify({'success': False, 'message': f'下载失败: {str(e)}'})


@app.route('/api/statistics', methods=['GET'])
def get_statistics():
    """获取总体统计信息"""
    try:
        output_dir = Path(CRAWLER_CONFIG.get('output_dir', 'xiaohongshu_data'))
        posts_dir = output_dir / 'posts'
        images_dir = output_dir / 'images'
        
        # 统计各关键词的数据
        keyword_stats = {}
        
        for json_file in posts_dir.glob("*.json"):
            try:
                # 从文件名提取关键词
                filename = json_file.stem
                parts = filename.split('_')
                if len(parts) >= 2:
                    keyword = parts[0]
                    
                    if keyword not in keyword_stats:
                        keyword_stats[keyword] = {
                            'post_count': 0,
                            'image_count': 0,
                            'total_likes': 0
                        }
                    
                    keyword_stats[keyword]['post_count'] += 1
                    
                    # 读取文件获取详细信息
                    with open(json_file, 'r', encoding='utf-8') as f:
                        post_data = json.load(f)
                        keyword_stats[keyword]['total_likes'] += post_data.get('like_count', 0)
                        
            except Exception:
                continue
                
        # 统计图片数量
        for keyword in keyword_stats:
            image_files = list(images_dir.glob(f"{keyword}_*"))
            keyword_stats[keyword]['image_count'] = len(image_files)
            
        return jsonify({
            'success': True,
            'data': keyword_stats
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取统计信息失败: {str(e)}'})


@socketio.on('connect')
def handle_connect():
    """WebSocket连接"""
    emit('log', {'level': 'info', 'message': '已连接到服务器'})


@socketio.on('disconnect')
def handle_disconnect():
    """WebSocket断开连接"""
    print('客户端断开连接')


if __name__ == '__main__':
    # 确保输出目录存在
    output_dir = Path(CRAWLER_CONFIG.get('output_dir', 'xiaohongshu_data'))
    output_dir.mkdir(exist_ok=True)
    (output_dir / 'posts').mkdir(exist_ok=True)
    (output_dir / 'images').mkdir(exist_ok=True)
    (output_dir / 'cache').mkdir(exist_ok=True)
    
    print("🚀 小红书爬虫Web界面启动中...")
    print("📱 访问地址: http://localhost:5000")
    print("⚠️  请确保已安装所有依赖包")
    
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)
