#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书推文和图片爬虫 - Selenium版本
使用Selenium处理JavaScript渲染的页面
"""

import os
import time
import json
import requests
import hashlib
import re
from datetime import datetime
from typing import List, Dict, Optional
import logging
from urllib.parse import urljoin, urlparse

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('xiaohongshu_selenium.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class XiaohongshuSeleniumCrawler:
    def __init__(self, output_dir: str = "xiaohongshu_data", headless: bool = False):
        """
        初始化爬虫
        
        Args:
            output_dir: 输出目录
            headless: 是否使用无头模式
        """
        self.output_dir = output_dir
        self.headless = headless
        self.driver = None
        self.wait = None
        self.create_directories()
        
    def create_directories(self):
        """创建必要的目录"""
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'images'), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'posts'), exist_ok=True)
        
    def setup_driver(self):
        """设置Chrome驱动"""
        try:
            chrome_options = Options()
            
            if self.headless:
                chrome_options.add_argument('--headless')
                
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 禁用图片加载以提高速度（可选）
            # prefs = {"profile.managed_default_content_settings.images": 2}
            # chrome_options.add_experimental_option("prefs", prefs)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            
            logger.info("Chrome驱动初始化成功")
            
        except Exception as e:
            logger.error(f"Chrome驱动初始化失败: {str(e)}")
            raise
            
    def close_driver(self):
        """关闭驱动"""
        if self.driver:
            self.driver.quit()
            logger.info("Chrome驱动已关闭")
            
    def clean_filename(self, filename: str) -> str:
        """清理文件名，移除非法字符"""
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        if len(filename) > 100:
            filename = filename[:100]
        return filename
        
    def download_image(self, image_url: str, save_path: str) -> bool:
        """下载图片"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Referer': 'https://www.xiaohongshu.com/'
            }
            
            response = requests.get(image_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            with open(save_path, 'wb') as f:
                f.write(response.content)
                
            logger.info(f"图片下载成功: {save_path}")
            return True
            
        except Exception as e:
            logger.error(f"图片下载失败 {image_url}: {str(e)}")
            return False
            
    def scroll_to_load_more(self, scroll_count: int = 3):
        """滚动页面加载更多内容"""
        for i in range(scroll_count):
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            logger.info(f"滚动加载 {i+1}/{scroll_count}")
            
    def extract_post_from_element(self, post_element) -> Optional[Dict]:
        """从页面元素提取推文信息"""
        try:
            post_info = {
                'id': '',
                'title': '',
                'content': '',
                'author': '',
                'author_id': '',
                'like_count': 0,
                'comment_count': 0,
                'share_count': 0,
                'create_time': '',
                'images': [],
                'tags': [],
                'url': ''
            }
            
            # 提取标题（通常在链接的title属性或文本中）
            try:
                title_element = post_element.find_element(By.CSS_SELECTOR, "a[title], .title, .note-title")
                post_info['title'] = title_element.get_attribute('title') or title_element.text
            except NoSuchElementException:
                pass
                
            # 提取作者信息
            try:
                author_element = post_element.find_element(By.CSS_SELECTOR, ".author, .user-name, .nickname")
                post_info['author'] = author_element.text
            except NoSuchElementException:
                pass
                
            # 提取链接
            try:
                link_element = post_element.find_element(By.CSS_SELECTOR, "a")
                post_info['url'] = link_element.get_attribute('href')
                # 从URL中提取ID
                if post_info['url']:
                    url_parts = post_info['url'].split('/')
                    for part in url_parts:
                        if len(part) > 10 and part.isalnum():
                            post_info['id'] = part
                            break
            except NoSuchElementException:
                pass
                
            # 提取图片
            try:
                img_elements = post_element.find_elements(By.CSS_SELECTOR, "img")
                for img in img_elements:
                    src = img.get_attribute('src')
                    if src and 'http' in src and 'avatar' not in src:
                        post_info['images'].append(src)
            except NoSuchElementException:
                pass
                
            # 如果没有提取到基本信息，返回None
            if not post_info['title'] and not post_info['images']:
                return None
                
            return post_info
            
        except Exception as e:
            logger.error(f"提取推文信息失败: {str(e)}")
            return None
            
    def save_post_data(self, post_info: Dict, keyword: str):
        """保存推文数据到文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{keyword}_{post_info['id']}_{timestamp}.json"
            filename = self.clean_filename(filename)
            
            filepath = os.path.join(self.output_dir, 'posts', filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(post_info, f, ensure_ascii=False, indent=2)
                
            logger.info(f"推文数据保存成功: {filepath}")
            
        except Exception as e:
            logger.error(f"保存推文数据失败: {str(e)}")
            
    def download_post_images(self, post_info: Dict, keyword: str) -> List[str]:
        """下载推文的所有图片"""
        downloaded_images = []
        
        for i, image_url in enumerate(post_info['images']):
            try:
                # 处理相对URL
                if image_url.startswith('//'):
                    image_url = 'https:' + image_url
                elif image_url.startswith('/'):
                    image_url = 'https://www.xiaohongshu.com' + image_url
                    
                # 生成文件名
                url_hash = hashlib.md5(image_url.encode()).hexdigest()[:8]
                ext = '.jpg'
                
                parsed_url = urlparse(image_url)
                if parsed_url.path:
                    path_ext = os.path.splitext(parsed_url.path)[1]
                    if path_ext in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
                        ext = path_ext
                        
                filename = f"{keyword}_{post_info['id']}_{i+1}_{url_hash}{ext}"
                filename = self.clean_filename(filename)
                
                save_path = os.path.join(self.output_dir, 'images', filename)
                
                if self.download_image(image_url, save_path):
                    downloaded_images.append(save_path)
                    
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"处理图片失败 {image_url}: {str(e)}")
                
        return downloaded_images
        
    def search_and_crawl(self, keyword: str, max_posts: int = 50, download_images: bool = True):
        """搜索并爬取推文"""
        try:
            self.setup_driver()
            
            # 访问小红书搜索页面
            search_url = f"https://www.xiaohongshu.com/search_result?keyword={keyword}"
            logger.info(f"访问搜索页面: {search_url}")
            
            self.driver.get(search_url)
            time.sleep(3)
            
            # 等待页面加载
            try:
                self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".feeds-container, .note-item, .search-result")))
            except TimeoutException:
                logger.warning("页面加载超时，尝试继续...")
                
            # 滚动加载更多内容
            self.scroll_to_load_more(5)
            
            # 查找推文元素
            post_selectors = [
                ".note-item",
                ".feeds-container .note",
                ".search-result .note",
                "[data-v-*] .note",
                ".waterfall-item"
            ]
            
            posts_found = []
            for selector in post_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        posts_found = elements
                        logger.info(f"找到 {len(elements)} 个推文元素 (使用选择器: {selector})")
                        break
                except Exception as e:
                    continue
                    
            if not posts_found:
                logger.warning("未找到推文元素，尝试通用选择器...")
                posts_found = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='/explore/']")
                
            logger.info(f"总共找到 {len(posts_found)} 个推文")
            
            crawled_count = 0
            for i, post_element in enumerate(posts_found):
                if crawled_count >= max_posts:
                    break
                    
                try:
                    # 滚动到元素位置
                    self.driver.execute_script("arguments[0].scrollIntoView();", post_element)
                    time.sleep(1)
                    
                    post_info = self.extract_post_from_element(post_element)
                    
                    if post_info and post_info['id']:
                        logger.info(f"处理推文 {crawled_count + 1}: {post_info['title'][:50]}...")
                        
                        # 保存推文数据
                        self.save_post_data(post_info, keyword)
                        
                        # 下载图片
                        if download_images and post_info['images']:
                            downloaded_images = self.download_post_images(post_info, keyword)
                            logger.info(f"下载了 {len(downloaded_images)} 张图片")
                            
                        crawled_count += 1
                        time.sleep(2)
                        
                except Exception as e:
                    logger.error(f"处理推文 {i} 失败: {str(e)}")
                    continue
                    
            logger.info(f"爬取完成，共处理 {crawled_count} 条推文")
            
        except Exception as e:
            logger.error(f"爬取过程出错: {str(e)}")
        finally:
            self.close_driver()


def main():
    """主函数"""
    print("小红书推文和图片爬虫 - Selenium版本")
    print("=" * 50)
    
    # 获取用户输入
    keyword = input("请输入搜索关键词: ").strip()
    if not keyword:
        print("关键词不能为空")
        return
        
    try:
        max_posts = int(input("请输入最大爬取数量 (默认20): ") or "20")
    except ValueError:
        max_posts = 20
        
    download_images = input("是否下载图片? (y/n, 默认y): ").strip().lower() != 'n'
    headless = input("是否使用无头模式? (y/n, 默认n): ").strip().lower() == 'y'
    
    # 创建爬虫实例并开始爬取
    crawler = XiaohongshuSeleniumCrawler(headless=headless)
    crawler.search_and_crawl(keyword, max_posts, download_images)


if __name__ == "__main__":
    main()
