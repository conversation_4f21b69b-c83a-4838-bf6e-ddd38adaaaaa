#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反爬虫检测模块
集成最新的小红书反爬虫技术
"""

import json
import time
import random
import hashlib
import hmac
import base64
import os
from typing import Dict, Any, Optional, List
from urllib.parse import urlencode, urlparse
import asyncio

from playwright.async_api import Page
from loguru import logger


class XSCommonGenerator:
    """x-s-common参数生成器"""
    
    def __init__(self):
        self.common_params = {
            'platform': 'web',
            'build': '3.52.0',
            'main_sign': '',
            'b1': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'x1': '1',
            'x2': '0',
            'x3': '1920x1080',
            'x4': '24',
            'x5': 'zh-CN',
            'x6': 'zh-CN,zh;q=0.9,en;q=0.8',
            'x7': 'Asia/Shanghai',
            'x8': '-480',
            'x9': 'd41d8cd98f00b204e9800998ecf8427e',
            'x10': '1'
        }
        
    async def generate_x_s_common(self, page: Page, url: str, data: Dict = None) -> str:
        """生成x-s-common参数"""
        try:
            # 注入x-s-common生成脚本
            await page.add_script_tag(content="""
                window.generateXSCommon = function(url, data) {
                    try {
                        // 这里需要根据最新的小红书算法实现
                        // 目前使用模拟的实现
                        const timestamp = Date.now();
                        const nonce = Math.random().toString(36).substring(2, 15);
                        
                        // 构建签名字符串
                        let signStr = url;
                        if (data) {
                            signStr += JSON.stringify(data);
                        }
                        signStr += timestamp + nonce;
                        
                        // 简化的签名算法（实际需要根据真实算法调整）
                        const signature = btoa(signStr).substring(0, 32);
                        
                        return {
                            'X-s': signature,
                            'X-t': timestamp.toString(),
                            'X-s-common': btoa(JSON.stringify({
                                h1: signature.substring(0, 8),
                                h2: timestamp.toString(),
                                h3: nonce
                            }))
                        };
                    } catch (e) {
                        console.error('生成x-s-common失败:', e);
                        return {};
                    }
                };
            """)
            
            # 调用生成函数
            result = await page.evaluate("""
                (url, data) => {
                    return window.generateXSCommon ? window.generateXSCommon(url, data) : {};
                }
            """, url, data)
            
            return result
            
        except Exception as e:
            logger.error(f"生成x-s-common失败: {str(e)}")
            return {}


class ProxyManager:
    """代理管理器"""
    
    def __init__(self, proxy_list: List[Dict] = None):
        self.proxy_list = proxy_list or []
        self.current_index = 0
        self.failed_proxies = set()
        
    def add_proxy(self, host: str, port: int, username: str = None, password: str = None):
        """添加代理"""
        proxy = {
            'host': host,
            'port': port,
            'username': username,
            'password': password
        }
        self.proxy_list.append(proxy)
        
    def get_next_proxy(self) -> Optional[Dict]:
        """获取下一个可用代理"""
        if not self.proxy_list:
            return None
            
        attempts = 0
        while attempts < len(self.proxy_list):
            proxy = self.proxy_list[self.current_index]
            proxy_key = f"{proxy['host']}:{proxy['port']}"
            
            if proxy_key not in self.failed_proxies:
                self.current_index = (self.current_index + 1) % len(self.proxy_list)
                return proxy
                
            self.current_index = (self.current_index + 1) % len(self.proxy_list)
            attempts += 1
            
        return None
        
    def mark_proxy_failed(self, proxy: Dict):
        """标记代理失败"""
        proxy_key = f"{proxy['host']}:{proxy['port']}"
        self.failed_proxies.add(proxy_key)
        logger.warning(f"代理失败: {proxy_key}")
        
    def reset_failed_proxies(self):
        """重置失败代理列表"""
        self.failed_proxies.clear()


class UserAgentRotator:
    """User-Agent轮换器"""
    
    def __init__(self):
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0',
        ]
        
    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        return random.choice(self.user_agents)


class RequestThrottler:
    """请求限流器"""
    
    def __init__(self, min_delay: float = 1.0, max_delay: float = 5.0):
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.last_request_time = 0
        
    async def wait(self):
        """等待适当的时间间隔"""
        current_time = time.time()
        elapsed = current_time - self.last_request_time
        
        # 随机延时
        delay = random.uniform(self.min_delay, self.max_delay)
        
        if elapsed < delay:
            wait_time = delay - elapsed
            await asyncio.sleep(wait_time)
            
        self.last_request_time = time.time()


class CookieManager:
    """Cookie管理器"""
    
    def __init__(self, cookie_file: str = None):
        self.cookie_file = cookie_file
        self.cookies = {}
        
    def load_cookies(self) -> Dict:
        """加载cookies"""
        if self.cookie_file and os.path.exists(self.cookie_file):
            try:
                with open(self.cookie_file, 'r', encoding='utf-8') as f:
                    self.cookies = json.load(f)
                logger.info("Cookies加载成功")
            except Exception as e:
                logger.error(f"加载cookies失败: {str(e)}")
        return self.cookies
        
    def save_cookies(self, cookies: List[Dict]):
        """保存cookies"""
        if self.cookie_file:
            try:
                with open(self.cookie_file, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, ensure_ascii=False, indent=2)
                logger.info("Cookies保存成功")
            except Exception as e:
                logger.error(f"保存cookies失败: {str(e)}")
                
    def is_login_valid(self, cookies: List[Dict]) -> bool:
        """检查登录状态是否有效"""
        # 检查关键cookie是否存在且未过期
        key_cookies = ['web_session', 'xsecappid', 'a1']
        
        cookie_dict = {cookie['name']: cookie for cookie in cookies}
        
        for key in key_cookies:
            if key not in cookie_dict:
                return False
                
            cookie = cookie_dict[key]
            if 'expires' in cookie:
                expires = cookie['expires']
                if expires < time.time():
                    return False
                    
        return True


class AntiDetectionManager:
    """反检测管理器"""
    
    def __init__(self):
        self.xs_generator = XSCommonGenerator()
        self.proxy_manager = ProxyManager()
        self.ua_rotator = UserAgentRotator()
        self.throttler = RequestThrottler()
        self.cookie_manager = CookieManager()
        
    async def setup_page_stealth(self, page: Page):
        """设置页面隐身模式"""
        # 注入反检测脚本
        await page.add_init_script("""
            // 隐藏webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 修改plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // 修改languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            
            // 添加chrome对象
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };
            
            // 修改权限查询
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            
            // 隐藏自动化指标
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            
            // 模拟真实的屏幕属性
            Object.defineProperty(screen, 'availTop', { get: () => 0 });
            Object.defineProperty(screen, 'availLeft', { get: () => 0 });
            Object.defineProperty(screen, 'availHeight', { get: () => screen.height });
            Object.defineProperty(screen, 'availWidth', { get: () => screen.width });
            
            // 模拟真实的时区
            Date.prototype.getTimezoneOffset = function() {
                return -480; // 中国时区
            };
        """)
        
    async def get_enhanced_headers(self, page: Page, url: str, data: Dict = None) -> Dict[str, str]:
        """获取增强的请求头"""
        # 生成x-s-common参数
        xs_params = await self.xs_generator.generate_x_s_common(page, url, data)
        
        # 基础请求头
        headers = {
            'User-Agent': self.ua_rotator.get_random_user_agent(),
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://www.xiaohongshu.com/',
            'Origin': 'https://www.xiaohongshu.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
        }
        
        # 添加x-s参数
        if xs_params:
            headers.update(xs_params)
            
        return headers
        
    async def simulate_human_behavior(self, page: Page):
        """模拟人类行为"""
        # 随机鼠标移动
        await page.mouse.move(
            random.randint(100, 800),
            random.randint(100, 600)
        )
        
        # 随机滚动
        scroll_distance = random.randint(100, 500)
        await page.evaluate(f'window.scrollBy(0, {scroll_distance})')
        
        # 随机等待
        await asyncio.sleep(random.uniform(0.5, 2.0))
        
    async def handle_verification(self, page: Page) -> bool:
        """处理验证码或人机验证"""
        try:
            # 检查是否出现验证码
            verification_selectors = [
                '.verification-code',
                '.captcha',
                '.slider-verify',
                '.geetest_holder'
            ]
            
            for selector in verification_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=3000)
                    if element:
                        logger.warning("检测到验证码，需要人工处理")
                        # 这里可以集成打码平台或人工处理
                        return False
                except:
                    continue
                    
            return True
            
        except Exception as e:
            logger.error(f"处理验证失败: {str(e)}")
            return False
            
    def get_proxy_config(self) -> Optional[Dict]:
        """获取代理配置"""
        proxy = self.proxy_manager.get_next_proxy()
        if proxy:
            config = {
                'server': f"http://{proxy['host']}:{proxy['port']}"
            }
            
            if proxy.get('username') and proxy.get('password'):
                config['username'] = proxy['username']
                config['password'] = proxy['password']
                
            return config
            
        return None
        
    async def wait_with_jitter(self):
        """带抖动的等待"""
        await self.throttler.wait()
        
        # 额外的随机抖动
        jitter = random.uniform(0.1, 1.0)
        await asyncio.sleep(jitter)
