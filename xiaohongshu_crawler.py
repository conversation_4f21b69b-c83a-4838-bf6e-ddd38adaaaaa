#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书推文和图片爬虫
支持根据关键词搜索并下载推文内容和配图
"""

import requests
import json
import os
import time
import re
from urllib.parse import urljoin, urlparse
from datetime import datetime
import hashlib
from typing import List, Dict, Optional
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('xiaohongshu_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class XiaohongshuCrawler:
    def __init__(self, output_dir: str = "xiaohongshu_data"):
        """
        初始化爬虫
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        self.session = requests.Session()
        self.setup_session()
        self.create_directories()
        
    def setup_session(self):
        """设置请求会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)
        
    def create_directories(self):
        """创建必要的目录"""
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'images'), exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, 'posts'), exist_ok=True)
        
    def clean_filename(self, filename: str) -> str:
        """清理文件名，移除非法字符"""
        # 移除或替换非法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        if len(filename) > 100:
            filename = filename[:100]
        return filename
        
    def download_image(self, image_url: str, save_path: str) -> bool:
        """
        下载图片
        
        Args:
            image_url: 图片URL
            save_path: 保存路径
            
        Returns:
            bool: 是否下载成功
        """
        try:
            response = self.session.get(image_url, timeout=30)
            response.raise_for_status()
            
            with open(save_path, 'wb') as f:
                f.write(response.content)
            
            logger.info(f"图片下载成功: {save_path}")
            return True
            
        except Exception as e:
            logger.error(f"图片下载失败 {image_url}: {str(e)}")
            return False
            
    def extract_post_info(self, post_data: Dict) -> Optional[Dict]:
        """
        从API响应中提取推文信息
        
        Args:
            post_data: 推文数据
            
        Returns:
            Dict: 提取的推文信息
        """
        try:
            # 这里需要根据实际的API响应结构来调整
            # 以下是一个示例结构，实际使用时需要根据真实API调整
            
            post_info = {
                'id': post_data.get('id', ''),
                'title': post_data.get('title', ''),
                'content': post_data.get('desc', ''),
                'author': post_data.get('user', {}).get('nickname', ''),
                'author_id': post_data.get('user', {}).get('user_id', ''),
                'like_count': post_data.get('interact_info', {}).get('liked_count', 0),
                'comment_count': post_data.get('interact_info', {}).get('comment_count', 0),
                'share_count': post_data.get('interact_info', {}).get('share_count', 0),
                'create_time': post_data.get('time', ''),
                'images': [],
                'tags': post_data.get('tag_list', [])
            }
            
            # 提取图片URL
            if 'image_list' in post_data:
                for img in post_data['image_list']:
                    if 'url' in img:
                        post_info['images'].append(img['url'])
            
            return post_info
            
        except Exception as e:
            logger.error(f"提取推文信息失败: {str(e)}")
            return None
            
    def save_post_data(self, post_info: Dict, keyword: str):
        """
        保存推文数据到文件
        
        Args:
            post_info: 推文信息
            keyword: 搜索关键词
        """
        try:
            # 创建文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{keyword}_{post_info['id']}_{timestamp}.json"
            filename = self.clean_filename(filename)
            
            filepath = os.path.join(self.output_dir, 'posts', filename)
            
            # 保存JSON数据
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(post_info, f, ensure_ascii=False, indent=2)
                
            logger.info(f"推文数据保存成功: {filepath}")
            
        except Exception as e:
            logger.error(f"保存推文数据失败: {str(e)}")
            
    def download_post_images(self, post_info: Dict, keyword: str) -> List[str]:
        """
        下载推文的所有图片
        
        Args:
            post_info: 推文信息
            keyword: 搜索关键词
            
        Returns:
            List[str]: 下载成功的图片路径列表
        """
        downloaded_images = []
        
        for i, image_url in enumerate(post_info['images']):
            try:
                # 生成图片文件名
                url_hash = hashlib.md5(image_url.encode()).hexdigest()[:8]
                ext = '.jpg'  # 默认扩展名
                
                # 尝试从URL获取扩展名
                parsed_url = urlparse(image_url)
                if parsed_url.path:
                    path_ext = os.path.splitext(parsed_url.path)[1]
                    if path_ext in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
                        ext = path_ext
                
                filename = f"{keyword}_{post_info['id']}_{i+1}_{url_hash}{ext}"
                filename = self.clean_filename(filename)
                
                save_path = os.path.join(self.output_dir, 'images', filename)
                
                # 下载图片
                if self.download_image(image_url, save_path):
                    downloaded_images.append(save_path)
                    
                # 添加延时避免请求过快
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"处理图片失败 {image_url}: {str(e)}")
                
        return downloaded_images
        
    def search_posts(self, keyword: str, page: int = 1, page_size: int = 20) -> List[Dict]:
        """
        搜索推文
        
        Args:
            keyword: 搜索关键词
            page: 页码
            page_size: 每页数量
            
        Returns:
            List[Dict]: 推文列表
        """
        # 注意：这里的API端点是示例，实际使用时需要：
        # 1. 找到真实的小红书API端点
        # 2. 处理反爬虫机制（如验证码、登录等）
        # 3. 可能需要使用selenium等工具模拟浏览器
        
        logger.warning("当前使用的是示例API端点，实际使用时需要替换为真实的小红书API")
        
        try:
            # 示例API调用（需要替换为真实API）
            url = "https://www.xiaohongshu.com/api/sns/web/v1/search/notes"
            params = {
                'keyword': keyword,
                'page': page,
                'page_size': page_size,
                'sort': 'general'  # 综合排序
            }
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # 解析响应数据（需要根据实际API响应调整）
            posts = []
            if 'data' in data and 'items' in data['data']:
                for item in data['data']['items']:
                    post_info = self.extract_post_info(item)
                    if post_info:
                        posts.append(post_info)
                        
            return posts
            
        except Exception as e:
            logger.error(f"搜索推文失败: {str(e)}")
            return []
            
    def crawl_by_keyword(self, keyword: str, max_posts: int = 50, download_images: bool = True):
        """
        根据关键词爬取推文
        
        Args:
            keyword: 搜索关键词
            max_posts: 最大爬取数量
            download_images: 是否下载图片
        """
        logger.info(f"开始爬取关键词: {keyword}")
        
        crawled_count = 0
        page = 1
        page_size = 20
        
        while crawled_count < max_posts:
            logger.info(f"正在爬取第 {page} 页...")
            
            posts = self.search_posts(keyword, page, page_size)
            
            if not posts:
                logger.info("没有更多推文了")
                break
                
            for post_info in posts:
                if crawled_count >= max_posts:
                    break
                    
                logger.info(f"处理推文: {post_info['title'][:50]}...")
                
                # 保存推文数据
                self.save_post_data(post_info, keyword)
                
                # 下载图片
                if download_images and post_info['images']:
                    downloaded_images = self.download_post_images(post_info, keyword)
                    logger.info(f"下载了 {len(downloaded_images)} 张图片")
                
                crawled_count += 1
                
                # 添加延时避免请求过快
                time.sleep(2)
                
            page += 1
            
        logger.info(f"爬取完成，共处理 {crawled_count} 条推文")


def main():
    """主函数"""
    # 创建爬虫实例
    crawler = XiaohongshuCrawler()
    
    # 设置爬取参数
    keyword = input("请输入搜索关键词: ").strip()
    if not keyword:
        print("关键词不能为空")
        return
        
    try:
        max_posts = int(input("请输入最大爬取数量 (默认50): ") or "50")
    except ValueError:
        max_posts = 50
        
    download_images = input("是否下载图片? (y/n, 默认y): ").strip().lower() != 'n'
    
    # 开始爬取
    crawler.crawl_by_keyword(keyword, max_posts, download_images)


if __name__ == "__main__":
    main()
