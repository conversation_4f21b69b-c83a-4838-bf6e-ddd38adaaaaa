<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小红书爬虫 - Web界面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .log-container {
            height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .log-info {
            color: #0f5132;
            background-color: #d1e7dd;
        }
        
        .log-error {
            color: #842029;
            background-color: #f8d7da;
        }
        
        .log-warning {
            color: #664d03;
            background-color: #fff3cd;
        }
        
        .stats-card {
            transition: transform 0.2s;
        }
        
        .stats-card:hover {
            transform: translateY(-2px);
        }
        
        .progress-container {
            display: none;
        }
        
        .config-section {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .result-item {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 15px;
            margin-bottom: 10px;
            background-color: #fff;
        }
        
        .image-preview {
            max-width: 100px;
            max-height: 100px;
            object-fit: cover;
            border-radius: 0.25rem;
            margin: 2px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 bg-light p-3">
                <h4 class="mb-4">
                    <i class="bi bi-robot"></i>
                    小红书爬虫
                </h4>
                
                <!-- 配置区域 -->
                <div class="config-section">
                    <h6><i class="bi bi-gear"></i> 爬取配置</h6>
                    
                    <div class="mb-3">
                        <label for="keyword" class="form-label">搜索关键词</label>
                        <input type="text" class="form-control" id="keyword" placeholder="请输入关键词">
                    </div>
                    
                    <div class="mb-3">
                        <label for="maxPosts" class="form-label">最大爬取数量</label>
                        <input type="number" class="form-control" id="maxPosts" value="50" min="1" max="1000">
                    </div>
                    
                    <div class="mb-3">
                        <label for="outputDir" class="form-label">输出目录</label>
                        <input type="text" class="form-control" id="outputDir" value="xiaohongshu_data">
                    </div>
                    
                    <div class="mb-3">
                        <label for="requestDelay" class="form-label">请求间隔(秒)</label>
                        <input type="number" class="form-control" id="requestDelay" value="2" min="1" max="10">
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="downloadImages" checked>
                        <label class="form-check-label" for="downloadImages">
                            下载图片
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="headless">
                        <label class="form-check-label" for="headless">
                            无头模式
                        </label>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" id="startBtn">
                            <i class="bi bi-play-fill"></i> 开始爬取
                        </button>
                        <button class="btn btn-danger" id="stopBtn" disabled>
                            <i class="bi bi-stop-fill"></i> 停止爬取
                        </button>
                    </div>
                </div>
                
                <!-- 进度显示 -->
                <div class="progress-container" id="progressContainer">
                    <h6><i class="bi bi-activity"></i> 爬取进度</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted" id="progressText">准备中...</small>
                </div>
            </div>
            
            <!-- 主内容区域 -->
            <div class="col-md-9 p-3">
                <!-- 标签页 -->
                <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button" role="tab">
                            <i class="bi bi-speedometer2"></i> 仪表板
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">
                            <i class="bi bi-terminal"></i> 日志
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="results-tab" data-bs-toggle="tab" data-bs-target="#results" type="button" role="tab">
                            <i class="bi bi-file-earmark-text"></i> 结果
                        </button>
                    </li>
                </ul>
                
                <div class="tab-content" id="mainTabContent">
                    <!-- 仪表板 -->
                    <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <h5>📊 统计概览</h5>
                                <div class="row" id="statsContainer">
                                    <div class="col-md-3">
                                        <div class="card stats-card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title text-primary">0</h5>
                                                <p class="card-text">总推文数</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stats-card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title text-success">0</h5>
                                                <p class="card-text">总图片数</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stats-card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title text-warning">0</h5>
                                                <p class="card-text">关键词数</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card stats-card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title text-info">0</h5>
                                                <p class="card-text">总点赞数</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h5>📈 关键词统计</h5>
                                <div class="table-responsive">
                                    <table class="table table-striped" id="keywordStatsTable">
                                        <thead>
                                            <tr>
                                                <th>关键词</th>
                                                <th>推文数</th>
                                                <th>图片数</th>
                                                <th>总点赞</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td colspan="5" class="text-center text-muted">暂无数据</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 日志 -->
                    <div class="tab-pane fade" id="logs" role="tabpanel">
                        <div class="mt-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="bi bi-terminal"></i> 实时日志</h5>
                                <button class="btn btn-sm btn-outline-secondary" id="clearLogsBtn">
                                    <i class="bi bi-trash"></i> 清空日志
                                </button>
                            </div>
                            <div class="log-container" id="logContainer">
                                <div class="log-entry log-info">
                                    <span class="text-muted">[系统]</span> 等待开始爬取...
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 结果 -->
                    <div class="tab-pane fade" id="results" role="tabpanel">
                        <div class="mt-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="bi bi-file-earmark-text"></i> 爬取结果</h5>
                                <div>
                                    <input type="text" class="form-control d-inline-block" id="searchKeyword" placeholder="输入关键词查看结果" style="width: 200px;">
                                    <button class="btn btn-primary ms-2" id="searchResultsBtn">
                                        <i class="bi bi-search"></i> 查看
                                    </button>
                                </div>
                            </div>
                            <div id="resultsContainer">
                                <div class="text-center text-muted">
                                    <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                                    <p>请输入关键词查看爬取结果</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    
    <script>
        // 全局变量
        let socket;
        let isRunning = false;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSocket();
            loadConfig();
            loadStatistics();
            bindEvents();
        });
        
        // 初始化WebSocket连接
        function initializeSocket() {
            socket = io();
            
            socket.on('connect', function() {
                addLog('info', '已连接到服务器');
            });
            
            socket.on('log', function(data) {
                addLog(data.level, data.message);
            });
            
            socket.on('crawl_complete', function(data) {
                addLog('info', `爬取完成！关键词: ${data.keyword}`);
                updateProgress(100, '爬取完成');
                stopCrawling();
                loadStatistics();
            });
            
            socket.on('crawl_finished', function(data) {
                stopCrawling();
            });
        }
        
        // 绑定事件
        function bindEvents() {
            document.getElementById('startBtn').addEventListener('click', startCrawling);
            document.getElementById('stopBtn').addEventListener('click', stopCrawling);
            document.getElementById('clearLogsBtn').addEventListener('click', clearLogs);
            document.getElementById('searchResultsBtn').addEventListener('click', searchResults);
        }
        
        // 加载配置
        function loadConfig() {
            fetch('/api/config')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('outputDir').value = data.output_dir || 'xiaohongshu_data';
                    document.getElementById('maxPosts').value = data.max_posts || 50;
                    document.getElementById('requestDelay').value = data.request_delay || 2;
                    document.getElementById('headless').checked = data.headless || false;
                    document.getElementById('downloadImages').checked = data.download_images !== false;
                })
                .catch(error => {
                    addLog('error', '加载配置失败: ' + error.message);
                });
        }
        
        // 开始爬取
        function startCrawling() {
            const keyword = document.getElementById('keyword').value.trim();
            if (!keyword) {
                alert('请输入搜索关键词');
                return;
            }
            
            const config = {
                keyword: keyword,
                max_posts: parseInt(document.getElementById('maxPosts').value),
                output_dir: document.getElementById('outputDir').value,
                download_images: document.getElementById('downloadImages').checked,
                headless: document.getElementById('headless').checked,
                request_delay: parseInt(document.getElementById('requestDelay').value)
            };
            
            fetch('/api/start_crawl', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    isRunning = true;
                    document.getElementById('startBtn').disabled = true;
                    document.getElementById('stopBtn').disabled = false;
                    document.getElementById('progressContainer').style.display = 'block';
                    updateProgress(0, '正在初始化...');
                    addLog('info', '爬取任务已启动');
                } else {
                    addLog('error', data.message);
                }
            })
            .catch(error => {
                addLog('error', '启动失败: ' + error.message);
            });
        }
        
        // 停止爬取
        function stopCrawling() {
            if (isRunning) {
                fetch('/api/stop_crawl', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    addLog('warning', data.message);
                });
            }
            
            isRunning = false;
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('progressContainer').style.display = 'none';
        }
        
        // 更新进度
        function updateProgress(percent, text) {
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }
        
        // 添加日志
        function addLog(level, message) {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${level}`;
            logEntry.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 清空日志
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }
        
        // 加载统计信息
        function loadStatistics() {
            fetch('/api/statistics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateDashboard(data.data);
                    }
                })
                .catch(error => {
                    console.error('加载统计信息失败:', error);
                });
        }
        
        // 更新仪表板
        function updateDashboard(stats) {
            let totalPosts = 0;
            let totalImages = 0;
            let totalLikes = 0;
            const keywordCount = Object.keys(stats).length;
            
            // 计算总数
            for (const keyword in stats) {
                totalPosts += stats[keyword].post_count;
                totalImages += stats[keyword].image_count;
                totalLikes += stats[keyword].total_likes;
            }
            
            // 更新统计卡片
            const statsCards = document.querySelectorAll('.stats-card .card-title');
            statsCards[0].textContent = totalPosts;
            statsCards[1].textContent = totalImages;
            statsCards[2].textContent = keywordCount;
            statsCards[3].textContent = totalLikes;
            
            // 更新关键词统计表
            updateKeywordTable(stats);
        }
        
        // 更新关键词统计表
        function updateKeywordTable(stats) {
            const tbody = document.querySelector('#keywordStatsTable tbody');
            tbody.innerHTML = '';
            
            if (Object.keys(stats).length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无数据</td></tr>';
                return;
            }
            
            for (const keyword in stats) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${keyword}</td>
                    <td>${stats[keyword].post_count}</td>
                    <td>${stats[keyword].image_count}</td>
                    <td>${stats[keyword].total_likes}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="downloadResults('${keyword}')">
                            <i class="bi bi-download"></i> 下载
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            }
        }
        
        // 搜索结果
        function searchResults() {
            const keyword = document.getElementById('searchKeyword').value.trim();
            if (!keyword) {
                alert('请输入关键词');
                return;
            }
            
            fetch(`/api/results/${keyword}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayResults(data.data, keyword);
                    } else {
                        document.getElementById('resultsContainer').innerHTML = 
                            `<div class="alert alert-warning">${data.message}</div>`;
                    }
                })
                .catch(error => {
                    document.getElementById('resultsContainer').innerHTML = 
                        `<div class="alert alert-danger">加载结果失败: ${error.message}</div>`;
                });
        }
        
        // 显示结果
        function displayResults(results, keyword) {
            const container = document.getElementById('resultsContainer');
            
            if (results.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                        <p>关键词 "${keyword}" 暂无结果</p>
                    </div>
                `;
                return;
            }
            
            let html = `
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6>关键词 "${keyword}" 的结果 (${results.length} 条)</h6>
                    <button class="btn btn-success btn-sm" onclick="downloadResults('${keyword}')">
                        <i class="bi bi-download"></i> 下载CSV
                    </button>
                </div>
            `;
            
            results.forEach(post => {
                html += `
                    <div class="result-item">
                        <div class="row">
                            <div class="col-md-8">
                                <h6>${post.title || '无标题'}</h6>
                                <p class="text-muted mb-2">${post.content ? post.content.substring(0, 200) + '...' : '无内容'}</p>
                                <small class="text-muted">
                                    作者: ${post.author || '未知'} | 
                                    点赞: ${post.like_count || 0} | 
                                    评论: ${post.comment_count || 0}
                                </small>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex flex-wrap">
                                    ${post.images ? post.images.slice(0, 4).map(img => 
                                        `<img src="${img}" class="image-preview" alt="预览图">`
                                    ).join('') : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // 下载结果
        function downloadResults(keyword) {
            window.open(`/api/download/${keyword}`, '_blank');
        }
        
        // 定期更新统计信息
        setInterval(loadStatistics, 30000); // 每30秒更新一次
    </script>
</body>
</html>
