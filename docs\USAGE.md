# 使用指南

## 快速开始

### 1. 环境准备

确保您的系统已安装：
- Python 3.8+
- Git（可选）

### 2. 安装项目

#### 方法一：自动安装（推荐）

```bash
# 下载项目
git clone https://github.com/your-repo/xiaohongshu-crawler.git
cd xiaohongshu-crawler

# 运行自动安装脚本
python setup_env.py
```

#### 方法二：手动安装

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 安装浏览器
playwright install chromium
```

### 3. 启动应用

#### 使用启动脚本（推荐）

```bash
# Windows
run_web.bat

# Linux/macOS
chmod +x run_web.sh
./run_web.sh
```

#### 手动启动

```bash
# 激活虚拟环境后
python app.py
```

### 4. 访问Web界面

打开浏览器访问：`http://localhost:5000`

## Web界面使用

### 主界面功能

1. **仪表板页面**
   - 配置爬取参数
   - 启动/停止爬取任务
   - 查看实时状态

2. **日志页面**
   - 实时查看爬取日志
   - 监控爬取进度
   - 错误信息提示

3. **结果页面**
   - 浏览爬取结果
   - 下载数据文件
   - 查看统计信息

### 参数配置说明

#### 基础参数

- **搜索关键词**: 要爬取的内容关键词，支持中文
- **最大数量**: 爬取的推文数量上限（建议不超过100）
- **输出目录**: 数据保存位置（默认：xiaohongshu_data）
- **请求间隔**: 请求之间的延时（秒），建议2-5秒

#### 高级参数

- **无头模式**: 是否隐藏浏览器窗口
- **下载图片**: 是否下载推文中的图片
- **登录模式**: 是否需要登录账号
- **代理设置**: 配置代理服务器（可选）

### 操作步骤

1. **配置参数**
   - 在仪表板页面填写搜索关键词
   - 设置合适的爬取数量和间隔
   - 选择输出目录

2. **开始爬取**
   - 点击"开始爬取"按钮
   - 切换到日志页面查看进度
   - 等待爬取完成

3. **查看结果**
   - 在结果页面浏览数据
   - 下载JSON或CSV文件
   - 查看图片文件

## 命令行使用

### 基础用法

```python
import asyncio
from xiaohongshu_advanced_crawler import XiaohongshuAdvancedCrawler

async def main():
    # 配置参数
    config = {
        'output_dir': 'xiaohongshu_data',
        'max_posts': 50,
        'headless': True,
        'request_delay': 2,
        'download_images': True
    }
    
    # 创建爬虫实例
    crawler = XiaohongshuAdvancedCrawler(config)
    
    # 开始爬取
    await crawler.crawl_by_keyword('美食', max_posts=50)
    
    # 关闭爬虫
    await crawler.close()

if __name__ == "__main__":
    asyncio.run(main())
```

### 高级用法

```python
import asyncio
from xiaohongshu_advanced_crawler import XiaohongshuAdvancedCrawler

async def advanced_crawl():
    config = {
        'output_dir': 'data',
        'max_posts': 100,
        'headless': False,  # 显示浏览器
        'request_delay': 3,
        'download_images': True,
        'login_required': True  # 需要登录
    }
    
    crawler = XiaohongshuAdvancedCrawler(config)
    
    try:
        # 登录（如果需要）
        await crawler.login()
        
        # 批量爬取多个关键词
        keywords = ['美食', '旅游', '时尚']
        for keyword in keywords:
            print(f"开始爬取关键词: {keyword}")
            await crawler.crawl_by_keyword(keyword, max_posts=30)
            
        # 导出数据
        await crawler.export_to_csv()
        
    finally:
        await crawler.close()

if __name__ == "__main__":
    asyncio.run(advanced_crawl())
```

## 配置文件

### config.py 配置说明

```python
# 基础配置
CRAWLER_CONFIG = {
    'output_dir': 'xiaohongshu_data',     # 输出目录
    'max_posts': 50,                      # 最大爬取数量
    'request_delay': 2,                   # 请求间隔
    'headless': False,                    # 无头模式
    'download_images': True,              # 下载图片
    'login_required': False,              # 是否需要登录
}

# 反爬虫配置
ANTI_DETECTION_CONFIG = {
    'use_proxy': False,                   # 是否使用代理
    'rotate_user_agent': True,            # 轮换User-Agent
    'simulate_human': True,               # 模拟人类行为
    'request_throttle': True,             # 请求限流
}

# Web界面配置
WEB_CONFIG = {
    'host': '0.0.0.0',                   # 监听地址
    'port': 5000,                        # 监听端口
    'debug': False,                      # 调试模式
}
```

### 自定义配置

您可以创建自己的配置文件：

```python
# my_config.py
CUSTOM_CONFIG = {
    'output_dir': 'my_data',
    'max_posts': 200,
    'request_delay': 5,
    'headless': True,
    'download_images': False,
}

# 使用自定义配置
from xiaohongshu_advanced_crawler import XiaohongshuAdvancedCrawler
from my_config import CUSTOM_CONFIG

crawler = XiaohongshuAdvancedCrawler(CUSTOM_CONFIG)
```

## 数据输出

### 文件结构

```
xiaohongshu_data/
├── posts/                    # 推文数据
│   ├── 美食_post_001.json
│   ├── 美食_post_002.json
│   └── ...
├── images/                   # 图片文件
│   ├── 美食_001_1_abc123.jpg
│   ├── 美食_001_2_def456.jpg
│   └── ...
├── cache/                    # 缓存文件
│   ├── cookies.json
│   └── login_state.json
└── exports/                  # 导出文件
    ├── 美食_posts.csv
    └── 美食_summary.json
```

### 数据格式

#### JSON格式（单个推文）

```json
{
  "id": "64a1b2c3d4e5f6789",
  "title": "超好吃的家常菜做法",
  "content": "今天分享一道简单易学的家常菜...",
  "author": "美食达人小王",
  "author_id": "user123456",
  "like_count": 1234,
  "comment_count": 56,
  "share_count": 78,
  "create_time": "2024-01-15 14:30:00",
  "images": [
    "https://sns-img-qc.xhscdn.com/xxx.jpg",
    "https://sns-img-qc.xhscdn.com/yyy.jpg"
  ],
  "tags": ["美食", "家常菜", "简单"],
  "url": "https://www.xiaohongshu.com/explore/64a1b2c3d4e5f6789",
  "note_type": "normal",
  "crawl_time": "2024-01-20 10:15:30"
}
```

#### CSV格式

包含所有推文的汇总数据，便于数据分析。

## 常见问题

### 安装问题

**Q: 安装依赖时出错**
A: 确保Python版本3.8+，尝试升级pip：`pip install --upgrade pip`

**Q: Playwright安装失败**
A: 手动安装：`playwright install chromium --force`

### 运行问题

**Q: 浏览器启动失败**
A: 检查系统权限，尝试以管理员身份运行

**Q: 爬取速度很慢**
A: 调整request_delay参数，检查网络连接

**Q: 登录失败**
A: 清除缓存文件，手动登录一次

### 数据问题

**Q: 爬取的数据不完整**
A: 增加等待时间，检查页面选择器

**Q: 图片下载失败**
A: 检查网络连接，确保有足够的存储空间

## 性能优化

### 提升爬取效率

1. **合理设置并发数**
   ```python
   config['max_concurrent'] = 3  # 不要设置过高
   ```

2. **使用代理池**
   ```python
   config['proxy_list'] = [
       'http://proxy1:8080',
       'http://proxy2:8080'
   ]
   ```

3. **优化选择器**
   - 使用更精确的CSS选择器
   - 减少不必要的等待时间

### 资源管理

1. **定期清理缓存**
   ```bash
   # 清理缓存文件
   rm -rf xiaohongshu_data/cache/*
   ```

2. **监控内存使用**
   - 避免一次性爬取过多数据
   - 及时释放不需要的资源

3. **磁盘空间管理**
   - 定期清理旧的数据文件
   - 压缩存储大文件

## 法律合规

### 使用原则

1. **遵守平台规则**
   - 不要过于频繁的请求
   - 尊重robots.txt协议
   - 遵守平台使用条款

2. **数据使用规范**
   - 仅用于学习研究
   - 不得商业化使用
   - 保护用户隐私

3. **技术伦理**
   - 不恶意攻击服务器
   - 不传播有害信息
   - 尊重知识产权

### 免责声明

本工具仅供学习和研究使用，使用者需自行承担使用风险和法律责任。开发者不对因使用本工具而产生的任何问题负责。

## 技术支持

如遇到问题，请按以下步骤排查：

1. 查看本文档的常见问题部分
2. 检查日志文件中的错误信息
3. 在GitHub上搜索相关Issue
4. 提交新的Issue并提供详细信息

---

更多详细信息请参考 [README.md](README.md) 文件。
