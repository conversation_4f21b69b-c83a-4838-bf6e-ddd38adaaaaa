# 小红书推文和图片爬虫

这是一个用于爬取小红书推文内容和配图的Python程序，支持根据关键词搜索并下载相关内容。

## 功能特性

- 🔍 根据关键词搜索小红书推文
- 📝 爬取推文标题、内容、作者等信息
- 🖼️ 自动下载推文配图
- 💾 数据保存为JSON格式，便于后续处理
- 🚀 支持两种爬取模式：API模式和Selenium模式
- ⚙️ 可配置的爬取参数
- 📊 详细的日志记录

## 文件说明

- `xiaohongshu_crawler.py` - 基础版本，使用requests库
- `xiaohongshu_selenium_crawler.py` - Selenium版本，处理JavaScript渲染页面
- `config.py` - 配置文件
- `requirements.txt` - 依赖包列表
- `README.md` - 使用说明

## 安装依赖

### 1. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 2. 安装Chrome浏览器和ChromeDriver

**Windows:**
1. 下载并安装Chrome浏览器
2. 下载对应版本的ChromeDriver: https://chromedriver.chromium.org/
3. 将ChromeDriver.exe放到系统PATH中或项目目录下

**Linux/Mac:**
```bash
# Ubuntu/Debian
sudo apt-get install google-chrome-stable

# 安装ChromeDriver
wget https://chromedriver.storage.googleapis.com/LATEST_RELEASE
LATEST=$(cat LATEST_RELEASE)
wget https://chromedriver.storage.googleapis.com/$LATEST/chromedriver_linux64.zip
unzip chromedriver_linux64.zip
sudo mv chromedriver /usr/local/bin/
```

## 使用方法

### 方法1：直接运行（推荐）

```bash
# 使用Selenium版本（推荐）
python xiaohongshu_selenium_crawler.py

# 使用基础版本
python xiaohongshu_crawler.py
```

### 方法2：编程调用

```python
from xiaohongshu_selenium_crawler import XiaohongshuSeleniumCrawler

# 创建爬虫实例
crawler = XiaohongshuSeleniumCrawler(
    output_dir="my_data",  # 输出目录
    headless=True          # 无头模式
)

# 开始爬取
crawler.search_and_crawl(
    keyword="美食",        # 搜索关键词
    max_posts=30,         # 最大爬取数量
    download_images=True  # 是否下载图片
)
```

## 输出结构

```
xiaohongshu_data/
├── posts/          # 推文数据（JSON格式）
│   ├── 美食_123456_20231201_143022.json
│   └── ...
├── images/         # 推文配图
│   ├── 美食_123456_1_abc123.jpg
│   ├── 美食_123456_2_def456.jpg
│   └── ...
└── xiaohongshu_crawler.log  # 日志文件
```

## 推文数据格式

```json
{
  "id": "推文ID",
  "title": "推文标题",
  "content": "推文内容",
  "author": "作者昵称",
  "author_id": "作者ID",
  "like_count": 点赞数,
  "comment_count": 评论数,
  "share_count": 分享数,
  "create_time": "发布时间",
  "images": ["图片URL列表"],
  "tags": ["标签列表"],
  "url": "推文链接"
}
```

## 配置说明

可以通过修改 `config.py` 文件来调整爬虫行为：

- `output_dir`: 输出目录
- `request_delay`: 请求间隔时间
- `headless`: 是否使用无头模式
- `scroll_count`: 页面滚动次数
- 更多配置项请查看 `config.py`

## 注意事项

### ⚠️ 重要提醒

1. **遵守法律法规**: 请确保你的爬虫行为符合当地法律法规
2. **尊重robots.txt**: 遵守网站的robots.txt规则
3. **合理使用**: 不要过于频繁地请求，避免给服务器造成压力
4. **数据使用**: 爬取的数据仅供个人学习研究使用，不得用于商业用途

### 🔧 技术说明

1. **反爬虫机制**: 小红书有较强的反爬虫机制，可能需要：
   - 登录账号
   - 处理验证码
   - 使用代理IP
   - 调整请求频率

2. **页面结构变化**: 网站页面结构可能会变化，需要相应调整选择器

3. **API限制**: 直接API调用可能受到限制，建议使用Selenium版本

## 故障排除

### 常见问题

1. **ChromeDriver版本不匹配**
   ```
   解决方案: 确保ChromeDriver版本与Chrome浏览器版本匹配
   ```

2. **页面加载超时**
   ```
   解决方案: 增加page_load_timeout配置或检查网络连接
   ```

3. **找不到元素**
   ```
   解决方案: 页面结构可能已变化，需要更新CSS选择器
   ```

4. **图片下载失败**
   ```
   解决方案: 检查图片URL是否有效，或添加适当的请求头
   ```

## 开发计划

- [ ] 支持更多搜索参数（时间范围、排序方式等）
- [ ] 添加数据去重功能
- [ ] 支持增量爬取
- [ ] 添加数据分析功能
- [ ] 支持多线程/异步爬取
- [ ] 添加GUI界面

## 许可证

本项目仅供学习和研究使用。使用时请遵守相关法律法规和网站服务条款。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 免责声明

本工具仅供学习和研究使用。用户在使用本工具时，应当遵守相关法律法规和网站的服务条款。作者不对因使用本工具而产生的任何法律责任承担责任。
