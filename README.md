# 小红书高级爬虫系统

一个功能强大的小红书内容爬虫，基于Playwright技术栈，集成最新反爬虫技术。

## 🚀 快速开始

### 1. 环境安装
```bash
python scripts/setup_env.py
```

### 2. 启动Web界面
```bash
# Windows
scripts\run_web.bat

# Linux/macOS  
./scripts/run_web.sh

# 或直接运行
python start_web.py
```

### 3. 访问界面
打开浏览器访问：http://localhost:5000

## 📁 项目结构

```
xiaohongshu/
├── src/                    # 源代码
│   ├── crawler/           # 爬虫核心模块
│   │   ├── xiaohongshu_advanced_crawler.py
│   │   └── anti_detection.py
│   ├── web/              # Web界面
│   │   ├── app.py
│   │   └── templates/
│   └── utils/            # 工具模块
├── scripts/              # 脚本文件
│   ├── setup_env.py      # 环境安装脚本
│   ├── run_web.bat       # Windows启动脚本
│   └── run_web.sh        # Linux/macOS启动脚本
├── config/               # 配置文件
│   ├── config.py         # 项目配置
│   └── requirements.txt  # 依赖包列表
├── docs/                 # 文档
│   ├── README.md         # 详细文档
│   └── USAGE.md          # 使用指南
├── data/                 # 数据输出
│   └── output/           # 爬取结果
│       ├── posts/        # 推文数据
│       ├── images/       # 图片文件
│       └── cache/        # 缓存文件
├── venv/                 # 虚拟环境
└── start_web.py          # Web应用启动入口
```

## ✨ 主要特性

- 🎯 **智能爬取**: 基于关键词精准爬取小红书内容
- 🛡️ **反爬虫技术**: 集成最新的x-s参数处理、代理轮换等技术
- 🌐 **Web界面**: 用户友好的操作界面，实时监控爬取进度
- 📊 **数据导出**: 支持JSON、CSV格式导出
- 🖼️ **图片下载**: 自动下载推文配图
- ⚡ **高性能**: 基于Playwright异步处理

## 📖 详细文档

- [完整文档](docs/README.md)
- [使用指南](docs/USAGE.md)

## ⚠️ 免责声明

本工具仅供学习研究使用，请遵守相关法律法规和平台使用条款。
