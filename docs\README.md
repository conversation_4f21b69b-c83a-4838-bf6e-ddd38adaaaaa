# 小红书高级爬虫 🚀

基于最新技术栈的小红书内容爬虫，集成了先进的反爬虫技术和用户友好的Web界面。

## ✨ 特性

- 🎯 **智能爬取**: 基于关键词精准搜索小红书内容
- 🛡️ **反爬虫技术**: 集成最新的反检测技术，包括x-s参数处理、代理轮换等
- 🌐 **Web界面**: 直观的网页操作界面，实时日志显示
- 📊 **数据分析**: 自动生成统计报告和数据可视化
- 💾 **多格式导出**: 支持JSON、CSV等多种数据格式
- 🔄 **异步处理**: 高性能异步爬取，支持大规模数据采集
- 🎭 **登录管理**: 智能登录状态保持，支持二维码登录

## 🏗️ 技术架构

### 核心技术栈
- **Playwright**: 现代浏览器自动化，替代Selenium
- **Flask**: Web界面框架
- **AsyncIO**: 异步编程支持
- **Pydantic**: 数据验证和序列化
- **Pandas**: 数据处理和分析

### 反爬虫技术
- **x-s-common参数**: 动态生成小红书加密参数
- **User-Agent轮换**: 模拟不同浏览器环境
- **代理池管理**: IP轮换避免封禁
- **请求限流**: 智能延时控制
- **人机行为模拟**: 鼠标移动、滚动等真实操作

## 📦 安装部署

### 方法一：自动安装（推荐）

1. **运行自动安装脚本**
```bash
python setup_env.py
```

2. **启动Web界面**
```bash
python app.py
```

### 方法二：手动安装

1. **创建虚拟环境**
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **安装Playwright浏览器**
```bash
playwright install chromium
```

4. **启动应用**
```bash
python app.py
```

## 🚀 快速开始

### Web界面使用

1. **访问界面**: 打开浏览器访问 `http://localhost:5000`

2. **配置参数**:
   - 搜索关键词：输入要爬取的内容关键词
   - 最大数量：设置爬取的推文数量上限
   - 输出目录：指定数据保存位置
   - 其他选项：请求间隔、是否下载图片等

3. **开始爬取**: 点击"开始爬取"按钮

4. **监控进度**: 在日志页面查看实时爬取状态

5. **查看结果**: 在结果页面浏览和下载数据

### 命令行使用

```python
import asyncio
from xiaohongshu_advanced_crawler import XiaohongshuAdvancedCrawler

async def main():
    config = {
        'output_dir': 'xiaohongshu_data',
        'max_posts': 50,
        'headless': True,
        'request_delay': 2
    }
    
    crawler = XiaohongshuAdvancedCrawler(config)
    await crawler.crawl_by_keyword('美食', max_posts=50)

if __name__ == "__main__":
    asyncio.run(main())
```

## 📁 项目结构

```
xiaohongshu-crawler/
├── xiaohongshu_advanced_crawler.py    # 主爬虫类
├── anti_detection.py                  # 反爬虫技术模块
├── app.py                            # Web应用主文件
├── config.py                         # 配置文件
├── setup_env.py                      # 环境安装脚本
├── requirements.txt                  # 依赖包列表
├── templates/
│   └── index.html                    # Web界面模板
├── xiaohongshu_data/                 # 数据输出目录
│   ├── posts/                        # 推文数据
│   ├── images/                       # 图片文件
│   └── cache/                        # 缓存文件
└── README.md                         # 项目文档
```

## ⚙️ 配置说明

### 基础配置

```python
CRAWLER_CONFIG = {
    'output_dir': 'xiaohongshu_data',     # 输出目录
    'max_posts': 50,                      # 最大爬取数量
    'request_delay': 2,                   # 请求间隔(秒)
    'headless': False,                    # 是否无头模式
    'download_images': True,              # 是否下载图片
}
```

### 代理配置

```python
# 在anti_detection.py中配置代理
proxy_manager = ProxyManager()
proxy_manager.add_proxy('proxy_host', 8080, 'username', 'password')
```

### 高级配置

- **请求头自定义**: 在`anti_detection.py`中修改User-Agent列表
- **延时策略**: 调整`RequestThrottler`的参数
- **重试机制**: 修改`@retry`装饰器参数

## 📊 数据格式

### 推文数据结构

```json
{
  "id": "推文唯一ID",
  "title": "推文标题",
  "content": "推文内容",
  "author": "作者昵称",
  "author_id": "作者ID",
  "like_count": 点赞数,
  "comment_count": 评论数,
  "share_count": 分享数,
  "create_time": "发布时间",
  "images": ["图片URL列表"],
  "tags": ["标签列表"],
  "url": "推文链接",
  "note_type": "笔记类型"
}
```

### 输出文件

- **JSON文件**: `关键词_推文ID_时间戳.json`
- **CSV文件**: `关键词_posts.csv`
- **图片文件**: `关键词_推文ID_序号_哈希值.jpg`

## 🔧 故障排除

### 常见问题

1. **浏览器启动失败**
   ```bash
   # 重新安装Playwright浏览器
   playwright install chromium --force
   ```

2. **登录失败**
   - 检查网络连接
   - 清除缓存文件：删除`xiaohongshu_data/cache/`目录
   - 尝试手动登录

3. **爬取速度慢**
   - 调整`request_delay`参数
   - 检查代理配置
   - 优化网络环境

4. **数据不完整**
   - 增加重试次数
   - 检查页面选择器是否更新
   - 调整等待时间

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 关闭无头模式查看浏览器操作
config['headless'] = False
```

## 📈 性能优化

### 提升爬取效率

1. **并发控制**: 适当增加并发数量
2. **缓存策略**: 利用登录状态缓存
3. **选择器优化**: 使用更精确的CSS选择器
4. **网络优化**: 配置高质量代理

### 资源管理

- 定期清理缓存文件
- 监控内存使用情况
- 合理设置爬取数量上限

## ⚖️ 法律声明

本项目仅供学习和研究使用，请遵守以下原则：

1. **合规使用**: 遵守小红书平台的使用条款
2. **数据保护**: 不得用于商业用途或侵犯他人隐私
3. **频率控制**: 避免对平台造成过大负担
4. **内容尊重**: 尊重原创作者的知识产权

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境设置

1. Fork本项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建Pull Request

### 代码规范

- 遵循PEP 8编码规范
- 添加适当的注释和文档
- 编写单元测试
- 更新相关文档

## 📞 支持与反馈

如有问题或建议，请通过以下方式联系：

- **Issues**: 在GitHub上提交问题
- **讨论**: 参与项目讨论
- **邮箱**: 发送邮件咨询

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Playwright](https://playwright.dev/) - 现代浏览器自动化
- [Flask](https://flask.palletsprojects.com/) - 轻量级Web框架
- [MediaCrawler](https://github.com/NanmiCoder/MediaCrawler) - 灵感来源

---

⭐ 如果这个项目对你有帮助，请给个Star支持一下！
