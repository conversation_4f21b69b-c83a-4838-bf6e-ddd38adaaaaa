#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟环境自动化设置脚本
支持Windows、Linux、macOS
"""

import os
import sys
import subprocess
import platform
import venv
from pathlib import Path


class VirtualEnvManager:
    def __init__(self, project_name="xiaohongshu_crawler"):
        self.project_name = project_name
        self.project_dir = Path.cwd()
        self.venv_dir = self.project_dir / "venv"
        self.system = platform.system().lower()
        
    def get_python_executable(self):
        """获取Python可执行文件路径"""
        if self.system == "windows":
            return self.venv_dir / "Scripts" / "python.exe"
        else:
            return self.venv_dir / "bin" / "python"
            
    def get_pip_executable(self):
        """获取pip可执行文件路径"""
        if self.system == "windows":
            return self.venv_dir / "Scripts" / "pip.exe"
        else:
            return self.venv_dir / "bin" / "pip"
            
    def get_activate_script(self):
        """获取激活脚本路径"""
        if self.system == "windows":
            return self.venv_dir / "Scripts" / "activate.bat"
        else:
            return self.venv_dir / "bin" / "activate"
            
    def check_python_version(self):
        """检查Python版本"""
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            raise RuntimeError(f"需要Python 3.8或更高版本，当前版本: {version.major}.{version.minor}")
        print(f"✓ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
        
    def create_virtual_env(self):
        """创建虚拟环境"""
        if self.venv_dir.exists():
            print(f"虚拟环境已存在: {self.venv_dir}")
            return
            
        print(f"正在创建虚拟环境: {self.venv_dir}")
        try:
            venv.create(self.venv_dir, with_pip=True)
            print("✓ 虚拟环境创建成功")
        except Exception as e:
            raise RuntimeError(f"创建虚拟环境失败: {str(e)}")
            
    def upgrade_pip(self):
        """升级pip"""
        print("正在升级pip...")
        try:
            subprocess.run([
                str(self.get_python_executable()),
                "-m", "pip", "install", "--upgrade", "pip"
            ], check=True, capture_output=True, text=True)
            print("✓ pip升级成功")
        except subprocess.CalledProcessError as e:
            print(f"⚠ pip升级失败: {e.stderr}")
            
    def install_requirements(self):
        """安装依赖包"""
        requirements_file = self.project_dir / "requirements.txt"
        if not requirements_file.exists():
            print("⚠ requirements.txt文件不存在")
            return
            
        print("正在安装依赖包...")
        try:
            subprocess.run([
                str(self.get_pip_executable()),
                "install", "-r", str(requirements_file)
            ], check=True)
            print("✓ 依赖包安装成功")
        except subprocess.CalledProcessError as e:
            raise RuntimeError(f"依赖包安装失败: {str(e)}")
            
    def install_playwright_browsers(self):
        """安装Playwright浏览器"""
        print("正在安装Playwright浏览器...")
        try:
            subprocess.run([
                str(self.get_python_executable()),
                "-m", "playwright", "install"
            ], check=True)
            print("✓ Playwright浏览器安装成功")
        except subprocess.CalledProcessError as e:
            print(f"⚠ Playwright浏览器安装失败: {str(e)}")
            
    def create_env_file(self):
        """创建环境配置文件"""
        env_file = self.project_dir / ".env"
        if env_file.exists():
            print("✓ .env文件已存在")
            return
            
        env_content = """# 小红书爬虫配置文件

# 数据库配置
DATABASE_URL=sqlite:///xiaohongshu.db

# 输出目录
OUTPUT_DIR=xiaohongshu_data

# 爬虫配置
MAX_POSTS=50
DOWNLOAD_IMAGES=true
REQUEST_DELAY=2
HEADLESS=false

# 代理配置（可选）
# PROXY_HOST=
# PROXY_PORT=
# PROXY_USERNAME=
# PROXY_PASSWORD=

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=xiaohongshu_crawler.log
"""
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✓ .env配置文件创建成功")
        
    def create_run_scripts(self):
        """创建运行脚本"""
        # Windows批处理脚本
        if self.system == "windows":
            bat_content = f"""@echo off
cd /d "{self.project_dir}"
call "{self.get_activate_script()}"
python app.py
pause
"""
            with open(self.project_dir / "run.bat", 'w', encoding='utf-8') as f:
                f.write(bat_content)
                
        # Unix shell脚本
        sh_content = f"""#!/bin/bash
cd "{self.project_dir}"
source "{self.get_activate_script()}"
python app.py
"""
        sh_file = self.project_dir / "run.sh"
        with open(sh_file, 'w', encoding='utf-8') as f:
            f.write(sh_content)
        
        # 设置执行权限
        if self.system != "windows":
            os.chmod(sh_file, 0o755)
            
        print("✓ 运行脚本创建成功")
        
    def print_usage_instructions(self):
        """打印使用说明"""
        activate_cmd = ""
        if self.system == "windows":
            activate_cmd = f"call {self.get_activate_script()}"
        else:
            activate_cmd = f"source {self.get_activate_script()}"
            
        print("\n" + "="*60)
        print("🎉 环境设置完成！")
        print("="*60)
        print("\n📋 使用说明:")
        print(f"1. 激活虚拟环境:")
        print(f"   {activate_cmd}")
        print(f"\n2. 运行爬虫程序:")
        print(f"   python app.py")
        print(f"\n3. 或者直接运行脚本:")
        if self.system == "windows":
            print(f"   run.bat")
        else:
            print(f"   ./run.sh")
        print(f"\n4. 配置文件位置:")
        print(f"   .env - 环境配置")
        print(f"   config.py - 爬虫配置")
        print("\n⚠️  注意事项:")
        print("- 首次运行需要扫码登录小红书")
        print("- 请遵守相关法律法规，仅用于学习研究")
        print("- 建议设置合理的请求间隔，避免被封")
        print("="*60)
        
    def setup(self):
        """执行完整的环境设置"""
        try:
            print("🚀 开始设置小红书爬虫环境...")
            print("-" * 40)
            
            # 检查Python版本
            self.check_python_version()
            
            # 创建虚拟环境
            self.create_virtual_env()
            
            # 升级pip
            self.upgrade_pip()
            
            # 安装依赖包
            self.install_requirements()
            
            # 安装Playwright浏览器
            self.install_playwright_browsers()
            
            # 创建配置文件
            self.create_env_file()
            
            # 创建运行脚本
            self.create_run_scripts()
            
            # 打印使用说明
            self.print_usage_instructions()
            
        except Exception as e:
            print(f"\n❌ 环境设置失败: {str(e)}")
            sys.exit(1)


def main():
    """主函数"""
    print("小红书爬虫环境自动化设置工具")
    print("支持Windows、Linux、macOS")
    print("=" * 50)
    
    manager = VirtualEnvManager()
    manager.setup()


if __name__ == "__main__":
    main()
